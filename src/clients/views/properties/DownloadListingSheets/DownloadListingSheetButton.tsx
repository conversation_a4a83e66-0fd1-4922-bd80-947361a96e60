'use client';

import LoadingSpinner from '@/clients/ui/loading-spinner';
import dynamic from 'next/dynamic';
import { ReactNode, useCallback, useState } from 'react';

const DownloadListingSheetModal = dynamic(
  () => import('./DownloadListingSheetModal'),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
        <LoadingSpinner className="w-10 h-10 text-olive" />
      </div>
    ),
  }
);

type Props = {
  propertyIds: number[];
  children?: ReactNode;
  className?: string;
};

const DownloadListingSheetButton = ({
  propertyIds = [],
  children,
  className = '',
}: Props) => {
  const [showModal, setShowModal] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setShowModal(false);
  }, []);

  return (
    <>
      <div onClick={() => setShowModal(true)} className={className}>
        {children}
      </div>
      {showModal && (
        <DownloadListingSheetModal
          onClose={onClose}
          propertyIds={propertyIds}
        />
      )}
    </>
  );
};

export default DownloadListingSheetButton;
