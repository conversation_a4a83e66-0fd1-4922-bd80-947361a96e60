import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export async function POST(req: Request) {
  const { listings } = await req.json();
  const token = cookies().get('token')?.value;

  try {
    const res = await fetch(`${BASE_URL}/download-listing-pdf`, {
      headers: {
        Authorization: `JWT ${token}`,
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify({ listings }),
    });

    if (!res.ok) {
      throw new Error('Error downloading listing sheet');
    }

    const blob = await res.blob();

    return new Response(blob, {
      headers: {
        'Content-Type': 'application/pdf',
      },
    });
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to download listing sheet');
  }
}
